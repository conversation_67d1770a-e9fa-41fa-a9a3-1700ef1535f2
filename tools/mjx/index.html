<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>买家秀欣赏-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            touch-action: manipulation;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🛍️';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .mjx-container {
            margin: 20px 0;
            border: 3px solid #333;
            border-radius: 8px;
            background: rgba(249, 249, 249, 0.95);
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .mjx-loading {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .mjx-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 4px;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.2);
        }

        .mjx-btn {
            background: #FF6B35;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 12px 4px;
            box-shadow: 3px 3px 0px #333;
            width: 100%;
        }

        .mjx-btn:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        .mjx-btn:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            .mjx-container {
                min-height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">买家秀欣赏-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🛍️ 买家秀欣赏</h1>
        
        <div class="mjx-container" id="mjxContainer">
            <div class="mjx-loading" id="mjxLoading">正在加载买家秀...</div>
            <img id="mjxImage" class="mjx-image" style="display: none;" alt="买家秀图片">
        </div>
        
        <button class="mjx-btn" onclick="refreshMjx()">🔄 刷新买家秀</button>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isLoading = false;

        // 加载买家秀图片
        function loadMjxImage() {
            if (isLoading) return;
            
            isLoading = true;
            const loading = document.getElementById('mjxLoading');
            const image = document.getElementById('mjxImage');
            const errorMsg = document.getElementById('errorMsg');
            
            // 显示加载状态
            loading.style.display = 'block';
            image.style.display = 'none';
            errorMsg.textContent = '';
            
            // 创建新的图片对象
            const img = new Image();
            
            img.onload = function() {
                image.src = this.src;
                image.style.display = 'block';
                loading.style.display = 'none';
                isLoading = false;
            };
            
            img.onerror = function() {
                loading.style.display = 'none';
                errorMsg.textContent = '加载失败，请重试';
                isLoading = false;
            };
            
            // 添加时间戳防止缓存
            img.src = 'https://api.uomg.com/api/rand.img3?&format=images&t=' + Date.now();
        }

        // 刷新买家秀
        function refreshMjx() {
            loadMjxImage();
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后自动加载第一张图片
        window.addEventListener('load', function() {
            loadMjxImage();
        });

        // 防止页面缩放
        document.addEventListener("gesturestart", function (e) {
            e.preventDefault();
        });

        document.addEventListener("gesturechange", function (e) {
            e.preventDefault();
        });

        document.addEventListener("gestureend", function (e) {
            e.preventDefault();
        });

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener("touchend", function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 防止键盘快捷键缩放
        document.addEventListener("keydown", function(e) {
            if ((e.ctrlKey || e.metaKey) && (e.keyCode === 61 || e.keyCode === 107 || e.keyCode === 173 || e.keyCode === 109 || e.keyCode === 187 || e.keyCode === 189)) {
                e.preventDefault();
            }
        });

        // 防止鼠标滚轮缩放
        document.addEventListener("wheel", function(e) {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
            }
        }, { passive: false });
    </script>
</body>
</html>
