<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>二维码生成器-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            touch-action: manipulation;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #8B5CF6 0%, #E91E63 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '';
            width: 16px;
            height: 16px;
            background: #4CAF50;
            border: 2px solid #333;
            display: inline-block;
        }



        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
            font-family: 'Courier New', monospace;
        }

        .size-group {
            display: flex;
            gap: 12px;
            align-items: end;
        }

        .size-group > div {
            flex: 1;
        }

        button {
            background: #8B5CF6;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .qr-result {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .qr-result.show {
            display: block;
        }

        #qrcode {
            margin: 16px 0;
            display: flex;
            justify-content: center;
        }

        #qrcode canvas {
            border: 4px solid #333;
            box-shadow: 4px 4px 0px #333;
        }

        .download-btn {
            background: #4CAF50;
        }

        .clear-btn {
            background: #f44336;
        }

        .error {
            color: #f44336;
            margin-top: 6px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }

            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }

            .container {
                margin: 0 8px;
                padding: 16px;
            }

            .app-title {
                font-size: 16px;
            }

            .subtitle {
                font-size: 20px;
            }

            h1 {
                font-size: 18px;
            }

            .size-group {
                flex-direction: column;
                gap: 8px;
            }

            button {
                width: 100%;
                margin: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>

    <div class="header-card">
        <h2 class="app-title">二维码生成-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>

    <div class="container">
        <h1>🔲 二维码生成器</h1>
        
        <div class="input-group">
            <label for="qrText">输入内容：</label>
            <textarea id="qrText" placeholder="请输入要生成二维码的内容（网址、文本等）"></textarea>
            <div class="error" id="textError"></div>
        </div>

        <div class="size-group">
            <div class="input-group">
                <label for="qrSize">二维码大小：</label>
                <select id="qrSize">
                    <option value="200">200x200</option>
                    <option value="300" selected>300x300</option>
                    <option value="400">400x400</option>
                    <option value="500">500x500</option>
                </select>
            </div>
            
            <div class="input-group">
                <label for="errorLevel">纠错级别：</label>
                <select id="errorLevel">
                    <option value="L">低 (L)</option>
                    <option value="M" selected>中 (M)</option>
                    <option value="Q">较高 (Q)</option>
                    <option value="H">高 (H)</option>
                </select>
            </div>
        </div>

        <button onclick="generateQR()">生成二维码</button>
        <button class="clear-btn" onclick="clearAll()">清空</button>

        <div class="qr-result" id="qrResult">
            <div id="qrcode"></div>
            <button class="download-btn" onclick="downloadQR()">下载二维码</button>
        </div>
    </div>

    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script>
        let currentQRCode = null;

        function generateQR() {
            const text = document.getElementById('qrText').value.trim();
            const size = parseInt(document.getElementById('qrSize').value);
            const errorLevel = document.getElementById('errorLevel').value;
            const textError = document.getElementById('textError');
            const qrResult = document.getElementById('qrResult');
            const qrcodeDiv = document.getElementById('qrcode');

            // 清除之前的错误信息
            textError.textContent = '';

            // 验证输入
            if (!text) {
                textError.textContent = '请输入要生成二维码的内容';
                qrResult.classList.remove('show');
                return;
            }

            try {
                // 创建二维码对象
                const qr = qrcode(0, errorLevel);
                qr.addData(text);
                qr.make();

                // 清除之前的二维码
                qrcodeDiv.innerHTML = '';

                // 创建二维码图像
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = size / modules;
                
                canvas.width = size;
                canvas.height = size;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                // 添加到页面
                qrcodeDiv.appendChild(canvas);
                currentQRCode = canvas;
                qrResult.classList.add('show');

            } catch (error) {
                textError.textContent = '生成二维码时出错，请检查输入内容';
                qrResult.classList.remove('show');
                console.error('QR Code generation error:', error);
            }
        }

        function downloadQR() {
            if (!currentQRCode) {
                alert('请先生成二维码');
                return;
            }

            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = 'qrcode.png';
                link.href = currentQRCode.toDataURL('image/png');
                
                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('下载失败，请重试');
                console.error('Download error:', error);
            }
        }

        function clearAll() {
            document.getElementById('qrText').value = '';
            document.getElementById('qrSize').value = '300';
            document.getElementById('errorLevel').value = 'M';
            document.getElementById('textError').textContent = '';
            document.getElementById('qrResult').classList.remove('show');
            document.getElementById('qrcode').innerHTML = '';
            currentQRCode = null;
        }

        // 回车键生成二维码
        document.getElementById('qrText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateQR();
            }
        });

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后聚焦到输入框
        window.addEventListener('load', function() {
            document.getElementById('qrText').focus();
        });
    
        // 防止页面缩放
        document.addEventListener("gesturestart", function (e) {
            e.preventDefault();
        });
        
        document.addEventListener("gesturechange", function (e) {
            e.preventDefault();
        });
        
        document.addEventListener("gestureend", function (e) {
            e.preventDefault();
        });
        
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener("touchend", function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 防止键盘快捷键缩放
        document.addEventListener("keydown", function(e) {
            if ((e.ctrlKey || e.metaKey) && (e.keyCode === 61 || e.keyCode === 107 || e.keyCode === 173 || e.keyCode === 109 || e.keyCode === 187 || e.keyCode === 189)) {
                e.preventDefault();
            }
        });
        
        // 防止鼠标滚轮缩放
        document.addEventListener("wheel", function(e) {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
            }
        }, { passive: false });
    </script>
</body>
</html>
