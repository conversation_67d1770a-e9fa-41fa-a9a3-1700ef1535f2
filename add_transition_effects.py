#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为tools目录下所有页面的返回按钮添加过渡效果
包括淡出动画和页面切换效果
"""

import os
import re
import glob

def add_transition_effects(file_path):
    """为单个文件添加过渡效果"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 检查是否已经有过渡效果
        if 'page-transition' in content or 'fadeOut' in content:
            return False  # 已经有相关效果，跳过
        
        # 1. 添加过渡效果的CSS样式
        transition_css = '''
        /* 页面过渡效果 */
        .page-transition {
            transition: all 0.3s ease-in-out;
        }
        
        .fade-out {
            opacity: 0;
            transform: translateY(-10px);
        }
        
        .back-btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .back-btn:hover::before {
            left: 100%;
        }
        
        .back-btn:active {
            transform: scale(0.98);
        }
        
        /* 点击波纹效果 */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
'''
        
        # 在现有CSS中添加过渡效果样式
        style_pattern = r'(html::-webkit-scrollbar\s*{[^}]*}\s*)'
        if re.search(style_pattern, content):
            content = re.sub(style_pattern, r'\1' + transition_css, content)
        else:
            # 如果没找到滚动条样式，在第一个CSS规则后添加
            first_rule_pattern = r'(\s*\*\s*{[^}]*}\s*)'
            content = re.sub(first_rule_pattern, r'\1' + transition_css, content)
        
        # 2. 修改body标签添加过渡类
        body_pattern = r'(<body[^>]*>)'
        content = re.sub(body_pattern, r'<body class="page-transition">', content)
        
        # 3. 添加增强的JavaScript函数
        enhanced_js = '''
        // 增强的返回函数，带过渡效果
        function goBack() {
            // 添加淡出效果
            document.body.classList.add('fade-out');
            
            // 延迟跳转，让动画完成
            setTimeout(function() {
                window.location.href = '/';
            }, 300);
        }
        
        // 添加按钮点击波纹效果
        function addRippleEffect(event) {
            const button = event.currentTarget;
            const ripple = document.createElement('div');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            button.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            // 为返回按钮添加波纹效果
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.addEventListener('click', addRippleEffect);
            }
            
            // 页面进入动画
            document.body.style.opacity = '0';
            document.body.style.transform = 'translateY(10px)';
            
            setTimeout(function() {
                document.body.style.transition = 'all 0.3s ease-in-out';
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 50);
        });
        
        // 防止页面后退时的闪烁
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                document.body.classList.remove('fade-out');
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }
        });
'''
        
        # 查找现有的goBack函数并替换
        goback_pattern = r'function goBack\(\)\s*{[^}]*}'
        if re.search(goback_pattern, content):
            # 替换现有的goBack函数
            content = re.sub(goback_pattern, '', content)
        
        # 在</script>标签前添加增强的JavaScript
        script_end_pattern = r'(\s*</script>)'
        content = re.sub(script_end_pattern, enhanced_js + r'\1', content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理文件时出错: {e}")
        return False

def main():
    """主函数：批量处理所有tools目录下的HTML文件"""
    tools_dir = "tools"
    
    if not os.path.exists(tools_dir):
        print(f"错误: {tools_dir} 目录不存在")
        return
    
    # 查找所有HTML文件
    html_files = glob.glob(os.path.join(tools_dir, "*/index.html"))
    
    if not html_files:
        print("未找到任何HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件，开始添加过渡效果...")
    print()
    
    success_count = 0
    for file_path in html_files:
        tool_name = os.path.basename(os.path.dirname(file_path))
        print(f"处理: {tool_name}")
        
        if add_transition_effects(file_path):
            success_count += 1
            print(f"  ✅ 已添加过渡效果")
        else:
            print(f"  ⏭️  跳过（已有效果或处理失败）")
    
    print()
    print(f"处理完成！成功为 {success_count} 个文件添加过渡效果")
    print()
    print("添加的过渡效果:")
    print("✅ 页面淡出动画（点击返回时）")
    print("✅ 页面淡入动画（页面加载时）")
    print("✅ 按钮悬停光扫效果")
    print("✅ 按钮点击波纹效果")
    print("✅ 按钮点击缩放效果")
    print("✅ 平滑的页面切换")

if __name__ == "__main__":
    main()
