<?php if (!defined('THINK_PATH')) exit();?><html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="/Public/static/annie/img/fovicon.ico">
    <title><?php echo ($con["webname"]); ?></title>
    <meta name="google-site-verification" content="FINDSr7GU2MfCZ9NVBBLPrgGHDXfffBCbywFI5YZWOs">
    <meta name="description" content="<?php echo ($con["webtitle"]); ?>">
    <meta name="keywords" content="<?php echo ($con["webtitle"]); ?>,一个完全免费的导航站">
    <meta name="author" content="<?php echo ($con["webtitle"]); ?>">
    <script defer="defer" src="/Public/static/annie/js/annie.js"></script>
     <!--<script defer="defer" src="/Public/static/annie/js/app.js"></script> -->
    <link href="/Public/static/annie/css/annie.css" rel="stylesheet">
    <link href="/Public/static/annie/css/app.css" rel="stylesheet">

    <style>
        /* 隐藏滚动条 */
        html, body {
            overflow-x: hidden;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏 Webkit 浏览器的滚动条 */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar {
            display: none;
        }

        /* 保持滚动功能但隐藏滚动条 */
        html {
            overflow-y: scroll;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        html::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        .ours-select {
            position: relative
        }

        .ours-select .selection-item {
            display: block;
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px;
            position: relative
        }

        .ours-select .selection-item .arrow-svg {
            position: absolute;
            display: inline-block;
            width: 14px;
            height: 14px;
            top: 50%;
            transform: translateY(-50%);
            right: 10px
        }

        .ours-select .selection-item .arrow-svg path {
            transition: all .3s
        }

        .ours-select .selection-item .arrow-fold::before {
            right: 50%;
            top: 75%;
            transform: rotate(45deg)
        }

        .ours-select .selection-item .arrow-fold::after {
            left: 50%;
            top: 75%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::before {
            right: 50%;
            top: 25%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::after {
            left: 50%;
            top: 25%;
            transform: rotate(45deg)
        }

        .ours-select .selector {
            top: 0;
            position: absolute;
            border-radius: 6px;
            overflow: hidden;
            width: 100%;
            background: #f7f7f7
        }

        .ours-select .selector:focus {
            outline: none
        }

        .ours-select .selector.z-index {
            z-index: 9999999999999;
            box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, .14)
        }

        .ours-select .selector .select-dropdown {
            transition: all .2s
        }

        .ours-select .selector .select-dropdown.unfold {
            max-height: 252px;
            overflow-y: auto;
            z-index: 9999999
        }

        .ours-select .selector .select-dropdown.fold {
            max-height: 0px;
            overflow-y: hidden
        }

        .ours-select .option-wrapper {
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px
        }

        .ours-select .option-wrapper:hover {
            background: #fff
        }

        .ours-select .seleted {
            background: #e6f4ff
        }

        .ours-select .option {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none
        }
    </style>
    <style>
        .hsq-image__error,
        .hsq-image__inner,
        .hsq-image__placeholder,
        .hsq-image__wrapper {
            height: 100%;
            width: 100%
        }

        .hsq-image {
            display: inline-block;
            overflow: hidden;
            position: relative
        }

        .hsq-image__inner {
            opacity: 1;
            vertical-align: top
        }

        .hsq-image__inner.is-loading {
            opacity: 0
        }

        .hsq-image__wrapper {
            left: 0;
            position: absolute;
            top: 0
        }

        .hsq-image__error,
        .hsq-image__placeholder {
            background: var(--hsq-fill-color-light)
        }

        .hsq-image__error {
            align-items: center;
            color: var(--hsq-text-color-placeholder);
            display: flex;
            font-size: 14px;
            justify-content: center;
            vertical-align: middle
        }

        .hsq-image__preview {
            cursor: pointer
        }

        .hsq-image-viewer__wrapper {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            top: 0
        }

        .hsq-image-viewer__btn {
            align-items: center;
            border-radius: 50%;
            box-sizing: border-box;
            cursor: pointer;
            display: flex;
            justify-content: center;
            opacity: .8;
            position: absolute;
            -webkit-user-select: none;
            user-select: none;
            z-index: 1
        }

        .hsq-image-viewer__btn .hsq-icon {
            cursor: pointer;
            font-size: inherit
        }

        .hsq-image-viewer__close {
            font-size: 40px;
            height: 40px;
            right: 40px;
            top: 40px;
            width: 40px
        }

        .hsq-image-viewer__canvas {
            align-items: center;
            display: flex;
            height: 100%;
            justify-content: center;
            position: static;
            -webkit-user-select: none;
            user-select: none;
            width: 100%
        }

        .hsq-image-viewer__actions {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            border-radius: 22px;
            bottom: 30px;
            height: 44px;
            left: 50%;
            padding: 0 23px;
            transform: translate(-50%);
            width: 282px
        }

        .hsq-image-viewer__actions__inner {
            align-items: center;
            color: #fff;
            cursor: default;
            display: flex;
            font-size: 23px;
            height: 100%;
            justify-content: space-around;
            width: 100%
        }

        .hsq-image-viewer__prev {
            left: 40px
        }

        .hsq-image-viewer__next,
        .hsq-image-viewer__prev {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            top: 50%;
            transform: translateY(-50%);
            width: 44px
        }

        .hsq-image-viewer__next {
            right: 40px;
            text-indent: 2px
        }

        .hsq-image-viewer__close {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            width: 44px
        }

        .hsq-image-viewer__mask {
            background: #000;
            height: 100%;
            left: 0;
            opacity: .5;
            position: absolute;
            top: 0;
            width: 100%
        }

        .viewer-fade-enter-active {
            animation: viewer-fade-in var(--hsq-transition-duration)
        }

        .viewer-fade-leave-active {
            animation: viewer-fade-out var(--hsq-transition-duration)
        }

        @keyframes viewer-fade-in {
            0% {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }

            to {
                opacity: 1;
                transform: translateZ(0)
            }
        }

        @keyframes viewer-fade-out {
            0% {
                opacity: 1;
                transform: translateZ(0)
            }

            to {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }
        }

        .hsq-popper.is-gj-update-tips {
            background: #ff9800;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 23, 54, .1);
            color: #fff;
            padding: 20px 32px 17px
        }

        .hsq-popper.is-gj-update-tips .hsq-popper__arrow:before {
            background: #ff9800
        }

        .gj-tooltips-info[data-v-ec77157e] {
            align-items: flex-end;
            display: flex;
            flex-direction: column;
            max-width: 400px
        }

        .gj-tooltips-info .gj-info[data-v-ec77157e] {
            font-size: 15px;
            text-align: left;
            width: 100%
        }

        .gj-tooltips-info .gj-info .gj-time[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 16px;
            font-weight: 700;
            justify-content: flex-start;
            margin: 8px 0
        }

        .gj-tooltips-info .gj-info .gj-time .hsq-icon[data-v-ec77157e] {
            font-size: 20px;
            margin-right: 10px
        }

        .gj-b-g[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            transform: scale(.95);
            transform-origin: right
        }

        .gj-b-g .gj-set-hide[data-v-ec77157e] {
            color: #fff;
            cursor: pointer;
            margin-right: 10px
        }

        .gj-icon-for-open[data-v-f706fe10] * {
            box-sizing: content-box
        }

        .gj-icon-for-open[data-v-f706fe10] {
            align-items: center;
            background-color: #fff5e6;
            border: 1px solid #ffcc80;
            border-bottom-right-radius: 50px;
            border-left: 0;
            border-top-right-radius: 50px;
            box-shadow: 0 0 9px 4px #ffeacc !important;
            box-sizing: content-box;
            color: #ff9800;
            cursor: pointer;
            display: flex;
            flex-direction: row;
            height: 28px;
            justify-content: center;
            padding-right: 10px;
            position: fixed;
            -webkit-user-select: none;
            z-index: 2147483647
        }

        .gj-icon-for-open>.hsq-icon[data-v-f706fe10] {
            font-size: 14px
        }

        .gj-icon[data-v-f706fe10] {
            border: 0;
            height: 18px;
            margin-left: 6px;
            padding: 0;
            pointer-events: none;
            width: 18px
        }

        .gt-text[data-v-f706fe10] {
            color: #ff9800;
            font-size: 12px;
            padding: 5px 0 5px 5px
        }

        .openGjGlobalTool[data-v-f706fe10] {
            color: #ff9800;
            padding: 5px 0
        }

        .gt-text[data-v-f706fe10],
        .openGjGlobalTool[data-v-f706fe10] {
            pointer-events: none
        }
     .topSize{
    margin-top: 2vw;
}   
        
        
        
     /* 教程视频图标样式 */
     .content-box.tab_cont img {
        width: 13vw;
        height: 13vw;
        border-radius: 3vw;
        box-shadow: 0 0 2.6666666667vw rgba(45, 98, 217, .15);
        margin-bottom: 3px;
        object-fit: contain;
        background: rgba(255,255,255,0.9);
    }






    @media screen and (min-width: 1200px) {
        /* 桌面端教程视频图标样式 */
        .content-box.tab_cont img {
            width: 4vw;
            height: 4vw;
            border-radius: .6vw;
            margin-bottom: 3px;
            object-fit: contain;
            background: rgba(255,255,255,0.9);
        }
    }






    </style>
</head>

<body inmaintabuse="1"><noscript><strong><?php echo ($con["gonggao"]); ?></strong></noscript>
    <div id="app" data-v-app="">
        <div>
            <div class="header flex-1">
                <div class="left flex-1"><img  src="/Public/static/annie/svg/btc.a3f304d0.svg" class="ava">
                    <div class="text">
                        <p>欢迎来<?php echo ($con["webname"]); ?></p>
                        <div class="text-min"> <?php echo ($con["webkeywords"]); ?> <img src="/Public/static/annie/svg/success.6ee344f6.svg"></div>
                    </div>
                </div>
                <div class="right"><img style="width:40px" src="/Public/static/annie/img/fovicon.ico"></div>
            </div>
            <!-- 移除pc-media包装，让所有设备显示相同内容 -->
                <div class="header-2 flex-2">
                    <p>欢迎光临本站</p>
                    <div class="big-text flex-1"><img src="/Public/static/annie/svg/lj.8e27ec23.svg" alt="链接" class="img-link">
                        <p class="flex-1"> <?php echo ($con["webkeywords"]); ?> <font></font>
                        </p><img src="/Public/static/annie/svg/kj.2d9f5d98.svg" alt="" class="logo">
                        <h1><?php echo ($con["webname"]); ?></h1>
                    </div>
                    <div class="info-text flex-2">
                        <p><?php echo ($con["webtitle"]); ?></p>
                        <div class="google flex-1">
                            <div class="google-logo">
                                <span>G</span><span>o</span><span>o</span><span>g</span><span>l</span><span>e</span>
                            </div><span>搜索-<?php echo ($con["webname"]); ?></span>
                        </div>
                    </div><!---->
                </div>
                <div class="main">
                    <div class="msg">
                        <div role="alert" class="van-notice-bar"
                            style="color: rgb(100, 120, 177); background: rgb(238, 240, 250);"><i
                                class="van-badge__wrapper van-icon van-notice-bar__left-icon"><!----><img
                                    class="van-icon__image"
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAAAclBMVEUAAABkeLFkeLFldbBkeLJkd7FkeLFkeLJkeLFkeLFkeLFkd7Fjd7Bsc7NkeLFkeLFkeLFkeLBkeLJkeLFleLJkeLFkeLFkeLJkebFjeLFkeLFjd7FkeLFkeLFldrRjebFjfLVkd7FkebJkebJlebFkeLFIT5GIAAAAJXRSTlMA4fkU2D4z7Oata0cZCPXEpW9eVCnbtJyUffDfzoojHQ/Ph2NOvYzgCQAAAOFJREFUKM990lduwzAURNER1XsvjmM77e5/i0GEJLIs0vPBn4MHPJKjXYo0DuVM5EHq1L4CApeeAPDsWDY8Yb/mnv10ebvT4cyOTxDc/jV/Yc+5gbP/q5lh422glnS7zBMcWFEFmfQKPHL50UkZJKWwcAf5OthbeTAE0ic0VtYCoQoY7RytaxkqOw8rVxg7t5DLh8TKfkxcqofJdbFWmn7O0cJFPUshmELD3KS2R/U9WLSmMwcu3iH5+/IwfuBrAmYr7TU41MF8ucsUxWPkqqI9l+esFiCQM72BVO6EQbLf6BvTLjcoxYWKzwAAAABJRU5ErkJggg=="><!----></i>
                            <div role="marquee" class="van-notice-bar__wrap">
                                <div class="van-notice-bar__content" style="transition-duration: 0s;">
                                   <marquee direction=left><?php echo ($con["gdgg"]); ?></marquee></span></div>
                            </div><!---->
                        </div>
                    </div>
                    <div class="main-box ">
                        <div class="main-1 nav">
                            <ul class="flex-1" id="tab">
                                <li class="tab li-active">付费工具</li>
                                <li class="tab">免费工具</li>
                                <li class="tab">教程视频</li>
                            </ul>
                            <div class="main-1-content ">
                                
                                
                                <div class="content-box tab_cont">
                                    <div class="content-list ">
                                        <?php if(is_array($mod)): foreach($mod as $key=>$vo): ?><div class="content-item"><a class="flex-2" href="<?php echo ($vo["link"]); ?>"
                                                title="<?php echo ($vo["title"]); ?>" ><img src="<?php echo ($vo["picname"]); ?>" alt="<?php echo ($vo["title"]); ?>">
                                                <div class="text-1"><span><?php echo ($vo["title"]); ?></span></div>
                                            </a></div><?php endforeach; endif; ?>	 
                     
                                    </div>
                                </div>
                                
                               <div class="content-box tab_cont" style="display:none;">
                                    <div class="content-list ">
                                        <?php if(is_array($hot)): foreach($hot as $key=>$vo): ?><div class="content-item"><a class="flex-2" href="<?php echo ($vo["link"]); ?>"
                                                title="<?php echo ($vo["title"]); ?>" ><img src="<?php echo ($vo["picname"]); ?>" alt="<?php echo ($vo["title"]); ?>">
                                                <div class="text-1"><span><?php echo ($vo["title"]); ?></span></div>
                                            </a></div><?php endforeach; endif; ?>	 
                     
                                    </div>
                                </div>
                                
                               <div class="content-box tab_cont" style="display:none;">
                                    <div class="content-list ">
                                        <?php if(is_array($video)): foreach($video as $key=>$vo): ?><div class="content-item">
                                           <a class="flex-2" href="<?php echo ($vo["picname"]); ?>"
                                                title="<?php echo ($vo["title"]); ?>" >
                                               <img src="/Public/uploads/App logo 015.png" alt="<?php echo ($vo["title"]); ?>"
                                                    style="width: 13vw; height: 13vw; border-radius: 3vw;
                                                           box-shadow: 0 0 2.6666666667vw rgba(45, 98, 217, .15);
                                                           margin-bottom: 3px; object-fit: contain;
                                                           background: rgba(255,255,255,0.9);">
                                                <div class="text-1"><span class="topSize"><?php echo ($vo["title"]); ?></span></div>
                                            </a>
                                            </div><?php endforeach; endif; ?>

                                    </div>
                                </div>
                                
                                
                                
                            </div>
                        </div>
                        
     
                        
                        
                        
                        
                        <div class="main-2" style="margin-top: 4vw;">
                            <div class="title">购买会员</div><a class="card card-bac2" href="/Index/utils/index" title="购买会员">
                                <div class="card-main flex-1"><img src="/Public/static/annie/svg/hkjgz.6b9722ca.svg" alt="购买会员"
                                        class="yh-28q">
                                    <div class="card-info">
                                        <p>购买ToKen</p>
                                        <p>购买会员卡密,限时低价,享最高权益</p>
                                    </div>
                                </div><img src="/Public/static/annie/svg/left-1.e3afa030.svg" alt="" class="left"><img
                                    src="/Public/static/annie/svg/hkj.48ef47d5.svg" alt="" class="logo-2">
                            </a>
                        </div>
                        <!-- <div class="main-2" style="margin-top: 4vw;">
                            <div class="title">优惠活动</div><a class="card card-bac2" href="/Index/cs/index" title="优惠活动大厅">
                                <div class="card-main flex-1"><img src="/Public/static/annie/svg/yh.b9c93529.svg" alt="优惠活动大厅"
                                        class="yh-28q">
                                    <div class="card-info">
                                        <p>优惠活动大厅</p>
                                        <p>各平台活动自助申请入口</p>
                                    </div>
                                </div><img src="/Public/static/annie/svg/left-1.e3afa030.svg" alt="" class="left"><img
                                    src="/Public/static/annie/svg/qian.2c2e8d46.svg" alt="" class="logo-2">
                            </a>
                        </div> -->
                        <div class="main-2" style="margin-top: 4vw;">
                            <div class="title">使用教程</div><a class="card card-bac2" href="/Index/video/index"
                                title="使用教程">
                                <div class="card-main flex-1"><img src="/Public/uploads/App logo 015.png" alt="使用教程"
                                        class="yh-28q" style="object-fit: contain; background: rgba(255,255,255,0.1); border-radius: 15%;">
                                    <div class="card-info">
                                        <p>使用教程</p>
                                        <p>更新更多热门教程合集</p>
                                    </div>
                                </div><img src="/Public/static/annie/svg/left-1.e3afa030.svg" alt="" class="left"><img
                                    src="/Public/uploads/App logo 015.png" alt="" class="logo-2" style="object-fit: contain; opacity: 0.15;">
                            </a>
                        </div>
                        <div class="main-2" style="margin-top: 4vw;">
                            <div class="title google-logo">
                                <span>G</span><span>o</span><span>o</span><span>g</span><span>l</span><span>e</span>
                            </div><a class="card" href="javascript:;" title="<?php echo ($con["webname"]); ?>">
                                <div class="card-main flex-1"><img src="/Public/static/annie/svg/chrome.ec49910c.svg" alt="<?php echo ($con["webname"]); ?>">
                                    <div class="card-info">
                                        <p>防止迷路</p>
                                        <p> 谷歌搜索" <font style="color: rgb(34, 78, 203);"><?php echo ($con["webname"]); ?></font>"即可获取本站 </p>
                                    </div>
                                </div><img src="/Public/static/annie/svg/left.2c979b64.svg" alt="" class="left">
                            </a>
                        </div>
                        

                        
                        
                                
                        <div class="main-3">
                              <?php if(is_array($foot)): foreach($foot as $key=>$vo): ?><a href="<?php echo ($vo["link"]); ?>" title="<?php echo ($vo["title"]); ?>">
                              
    
                            <img src="<?php echo ($vo["picname"]); ?>"
                                    alt="<?php echo ($vo["title"]); ?>">
                                <div class="main-3-info">
                                    <p>点击访问<?php echo ($vo["title"]); ?></p>
                                    <!--<p> 如遇本网站功能进不去的,请使用猫云vpn进行翻墙,低价高速,每月低至8元即可享翻墙的快乐 </p>-->
                                </div>
                            </a><?php endforeach; endif; ?>  
                        </div>
                        




                    </div>
                </div>
            <!-- 移除pc-media结束标签 -->
            <div data-v-4ad322e2="" class="footer-sj">
                <div data-v-4ad322e2="" class="footer-sj-main"></div><span data-v-4ad322e2=""><?php echo ($con["gonggao"]); ?><sup
                        data-v-4ad322e2="">®</sup></span>
            </div><!---->
        </div>
    </div>
    <script src="chrome-extension://bcjkohpafljnigdmnlaghjhaaleonlac/sm.bundle.js" data-pname="fatkun-mv3-new"
        data-asset-path="https://fkm3n.s3.ap-northeast-2.amazonaws.com"></script>
    <div id="__REC__-container"></div>
    <div id="hsq-popper-container-695"><!--v-if--></div><span data-v-app="">

    </span><fatkun-drop-panel style="display: none;"></fatkun-drop-panel>
    
    
  <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.js
"></script>

<script type="text/javascript">



$(document).ready(function(){
$("#tab li").click(function(){
var liindex = $("#tab li").index(this);
$("#tab").find("li").removeClass("li-active");
$(this).addClass("li-active");
$(".tab_cont").eq(liindex).show().siblings(".tab_cont").hide();
});
});

         


</script>  
    
    
</body>

</html>