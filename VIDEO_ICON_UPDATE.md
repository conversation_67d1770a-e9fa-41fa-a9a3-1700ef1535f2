# 使用教程视频图标更新说明

## 🎯 更新目标
将首页使用教程部分的视频图标替换为App logo 015.png，提升品牌一致性和视觉效果。

## 📁 修改的文件
- `Application/Index/View/Index/index.html` - 首页模板文件

## 🔧 具体修改内容

### 1. 主图标替换
**修改位置**: 使用教程卡片的主图标
**修改前**:
```html
<img src="__PUBLIC__/static/annie/svg/jm.735ff16b.svg" alt="使用教程" class="yh-28q">
```

**修改后**:
```html
<img src="__PUBLIC__/uploads/App logo 015.png" alt="使用教程" 
     class="yh-28q" style="object-fit: contain; background: rgba(255,255,255,0.1); border-radius: 15%;">
```

### 2. 背景装饰图标替换
**修改位置**: 使用教程卡片的右侧背景装饰图标
**修改前**:
```html
<img src="__PUBLIC__/static/annie/svg/jm-2.ed3f90d1.svg" alt="" class="logo-2">
```

**修改后**:
```html
<img src="__PUBLIC__/uploads/App logo 015.png" alt="" 
     class="logo-2" style="object-fit: contain; opacity: 0.15;">
```

## 🎨 样式优化

### 主图标样式
- **object-fit: contain**: 确保图片按比例缩放，不变形
- **background: rgba(255,255,255,0.1)**: 添加半透明白色背景，提升对比度
- **border-radius: 15%**: 添加圆角效果，与整体设计风格保持一致

### 背景装饰图标样式
- **object-fit: contain**: 保持图片比例
- **opacity: 0.15**: 设置为半透明效果，作为背景装饰不抢夺主要内容的注意力

## 📊 文件信息

### App logo 015.png
- **路径**: `Public/uploads/App logo 015.png`
- **大小**: 508,481 bytes (约500KB)
- **格式**: PNG
- **用途**: 品牌Logo，用于使用教程图标

## 🔍 技术细节

### CSS类说明
- **yh-28q**: 主图标样式类，控制图标的基础尺寸和布局
- **logo-2**: 背景装饰图标样式类，通常用于右侧装饰元素

### 响应式适配
原有的CSS类已经包含了响应式设计，新的PNG图标会自动适配不同屏幕尺寸：
- 移动端: 较大尺寸显示
- 桌面端: 较小尺寸显示

## ✅ 验证要点

1. **图标显示**: 确认App logo 015.png正确显示在使用教程卡片中
2. **样式效果**: 验证圆角、背景色和透明度效果
3. **响应式**: 测试不同屏幕尺寸下的显示效果
4. **品牌一致性**: 确保与其他页面的品牌元素保持一致

## 🚀 优势效果

1. **品牌统一**: 使用统一的品牌Logo，提升品牌识别度
2. **视觉优化**: PNG格式支持更好的图像质量和透明度
3. **用户体验**: 一致的视觉元素提升用户对品牌的认知
4. **维护便利**: 统一使用品牌Logo，便于后续维护和更新

## 📝 注意事项

1. **文件路径**: 确保App logo 015.png文件存在于指定路径
2. **文件权限**: 确认Web服务器对该文件有读取权限
3. **缓存清理**: 可能需要清理浏览器缓存才能看到更新效果
4. **备份**: 建议保留原始SVG文件作为备份

## 🔄 回滚方案

如需回滚到原始图标，可以将以下代码恢复：

```html
<!-- 主图标 -->
<img src="__PUBLIC__/static/annie/svg/jm.735ff16b.svg" alt="使用教程" class="yh-28q">

<!-- 背景装饰图标 -->
<img src="__PUBLIC__/static/annie/svg/jm-2.ed3f90d1.svg" alt="" class="logo-2">
```
