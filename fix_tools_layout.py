#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复tools目录下所有页面的布局宽度问题
确保顶栏、标题栏、功能栏三个板块宽度完全一致
"""

import os
import re
import glob

def fix_layout_consistency(file_path):
    """修复单个文件的布局一致性问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 修复 .top-bar 样式
        top_bar_pattern = r'(\.top-bar\s*{[^}]*?max-width:\s*480px;[^}]*?)(\})'
        def fix_top_bar(match):
            styles = match.group(1)
            # 添加统一的宽度设置
            if 'width: calc(100% - 32px);' not in styles:
                styles = styles.replace('max-width: 480px;', 'max-width: 480px;\n            width: calc(100% - 32px);')
            if 'box-sizing: border-box;' not in styles:
                styles += '\n            box-sizing: border-box;'
            return styles + match.group(2)
        
        content = re.sub(top_bar_pattern, fix_top_bar, content, flags=re.DOTALL)
        
        # 2. 修复 .header-card 样式
        header_card_pattern = r'(\.header-card\s*{[^}]*?max-width:\s*480px;[^}]*?)(\})'
        def fix_header_card(match):
            styles = match.group(1)
            if 'width: calc(100% - 32px);' not in styles:
                styles = styles.replace('max-width: 480px;', 'max-width: 480px;\n            width: calc(100% - 32px);')
            if 'box-sizing: border-box;' not in styles:
                styles += '\n            box-sizing: border-box;'
            return styles + match.group(2)
        
        content = re.sub(header_card_pattern, fix_header_card, content, flags=re.DOTALL)
        
        # 3. 修复 .container 样式
        container_pattern = r'(\.container\s*{[^}]*?max-width:\s*480px;[^}]*?)(\})'
        def fix_container(match):
            styles = match.group(1)
            # 替换 width: 100% 为 width: calc(100% - 32px)
            styles = re.sub(r'width:\s*100%;', 'width: calc(100% - 32px);', styles)
            if 'width: calc(100% - 32px);' not in styles:
                styles = styles.replace('max-width: 480px;', 'max-width: 480px;\n            width: calc(100% - 32px);')
            if 'box-sizing: border-box;' not in styles:
                styles += '\n            box-sizing: border-box;'
            return styles + match.group(2)
        
        content = re.sub(container_pattern, fix_container, content, flags=re.DOTALL)
        
        # 4. 修复移动端媒体查询
        media_query_pattern = r'(@media\s*\([^)]*max-width:\s*480px[^)]*\)\s*{[^}]*?)(\.top-bar\s*{[^}]*?margin:\s*0\s+8px[^}]*?}[^}]*?\.header-card\s*{[^}]*?margin:\s*0\s+8px[^}]*?}[^}]*?\.container\s*{[^}]*?margin:\s*0\s+8px[^}]*?}[^}]*?)(\})'
        def fix_media_query(match):
            media_start = match.group(1)
            media_content = match.group(2)
            media_end = match.group(3)
            
            # 为每个元素添加统一的宽度设置
            media_content = re.sub(r'(\.top-bar\s*{[^}]*?)', r'\1\n                width: calc(100% - 16px);', media_content)
            media_content = re.sub(r'(\.header-card\s*{[^}]*?)', r'\1\n                width: calc(100% - 16px);', media_content)
            media_content = re.sub(r'(\.container\s*{[^}]*?)', r'\1\n                width: calc(100% - 16px);', media_content)
            
            return media_start + media_content + media_end
        
        content = re.sub(media_query_pattern, fix_media_query, content, flags=re.DOTALL)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数：批量处理所有tools目录下的HTML文件"""
    tools_dir = "tools"
    
    if not os.path.exists(tools_dir):
        print(f"错误: {tools_dir} 目录不存在")
        return
    
    # 查找所有HTML文件
    html_files = glob.glob(os.path.join(tools_dir, "*/index.html"))
    
    if not html_files:
        print("未找到任何HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件，开始处理...")
    
    success_count = 0
    for file_path in html_files:
        print(f"处理: {file_path}")
        if fix_layout_consistency(file_path):
            success_count += 1
            print(f"  ✅ 已修复")
        else:
            print(f"  ⏭️  无需修改或处理失败")
    
    print(f"\n处理完成！成功修复 {success_count} 个文件")
    print("\n修复内容:")
    print("1. 统一三个板块的宽度为 calc(100% - 32px)")
    print("2. 添加 box-sizing: border-box 确保边框计算正确")
    print("3. 修复移动端媒体查询的宽度设置")
    print("4. 确保所有设备上三个板块宽度完全一致")

if __name__ == "__main__":
    main()
