#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量为tools目录下所有页面添加隐藏滚动条的CSS样式
保持滚动功能但隐藏滚动条的视觉显示
"""

import os
import re
import glob

def add_scrollbar_hiding_css(file_path):
    """为单个文件添加隐藏滚动条的CSS样式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 检查是否已经有隐藏滚动条的样式
        if 'scrollbar-width: none' in content or 'overflow-x: hidden' in content:
            return False  # 已经有相关样式，跳过
        
        # 查找 <style> 标签的开始位置
        style_start_pattern = r'(<style[^>]*>)'
        match = re.search(style_start_pattern, content)
        
        if not match:
            print(f"  ⚠️  未找到 <style> 标签")
            return False
        
        # 隐藏滚动条的CSS样式
        scrollbar_css = '''
        /* 隐藏滚动条 */
        html, body {
            overflow-x: hidden;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        /* 隐藏 Webkit 浏览器的滚动条 */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar {
            display: none;
        }
        
        /* 保持滚动功能但隐藏滚动条 */
        html {
            overflow-y: scroll;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        html::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
'''
        
        # 在 <style> 标签后插入CSS
        style_start = match.end()
        content = content[:style_start] + scrollbar_css + content[style_start:]
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理文件时出错: {e}")
        return False

def main():
    """主函数：批量处理所有tools目录下的HTML文件"""
    tools_dir = "tools"
    
    if not os.path.exists(tools_dir):
        print(f"错误: {tools_dir} 目录不存在")
        return
    
    # 查找所有HTML文件
    html_files = glob.glob(os.path.join(tools_dir, "*/index.html"))
    
    if not html_files:
        print("未找到任何HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件，开始添加隐藏滚动条样式...")
    print()
    
    success_count = 0
    for file_path in html_files:
        tool_name = os.path.basename(os.path.dirname(file_path))
        print(f"处理: {tool_name}")
        
        if add_scrollbar_hiding_css(file_path):
            success_count += 1
            print(f"  ✅ 已添加隐藏滚动条样式")
        else:
            print(f"  ⏭️  跳过（已有样式或处理失败）")
    
    print()
    print(f"处理完成！成功为 {success_count} 个文件添加隐藏滚动条样式")
    print()
    print("添加的CSS功能:")
    print("✅ 完全隐藏滚动条（所有浏览器）")
    print("✅ 保持页面滚动功能")
    print("✅ 支持鼠标滚轮滚动")
    print("✅ 支持触摸滑动")
    print("✅ 支持键盘导航")
    print("✅ 跨浏览器兼容")

if __name__ == "__main__":
    main()
